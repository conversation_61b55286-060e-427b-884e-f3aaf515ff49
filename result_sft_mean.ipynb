import pandas as pd
import seaborn as sns

df = pd.read_json("qwen3-14B-sft-mean.json")

df.head()

sns.histplot(df, x="json_valid")

(df["json_valid"]==1.0).sum()

sns.histplot(df, x="llm_score", bins=20)

(df["llm_score"]>=7).sum()

sns.histplot(df, x="top_level_keys_present")

sns.histplot(df, x="workflow_name_valid")

sns.histplot(df, x="percent_node_with_valid_type")

sns.histplot(df, x="percent_node_with_valid_version")

sns.histplot(df, x="percent_node_with_valid_structure")

sns.histplot(df, x="percent_node_with_parameters")

sns.histplot(df, x="percent_connections_with_valid_structure")

sns.histplot(df, x="percent_connections_with_valid_target_node")

sns.histplot(df, x="active_field_boolean")

