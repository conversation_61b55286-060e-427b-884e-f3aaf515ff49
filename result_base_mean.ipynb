import pandas as pd
import seaborn as sns

df = pd.read_json("qwen3-14B-base-mean.json")

df.head()

sns.histplot(df, x="json_valid")

sns.histplot(df, x="llm_score", bins=20)

(df["llm_score"]>=7).sum()

sns.histplot(df, x="top_level_keys_present")

sns.histplot(df, x="percent_node_with_valid_type")

sns.histplot(df, x="percent_node_with_valid_version")

sns.histplot(df, x="percent_node_with_valid_structure")

sns.histplot(df, x="percent_node_with_parameters")

sns.histplot(df, x="percent_connections_with_valid_structure")

sns.histplot(df, x="percent_connections_with_valid_target_node")

