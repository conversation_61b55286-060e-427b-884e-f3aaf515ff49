#!/usr/bin/env python3
"""
Example usage of the JSON comparison tool
"""

import subprocess
import os

def run_comparison(file1, file2, description=""):
    """Run the comparison tool with two files"""
    print(f"\n{'='*60}")
    print(f"Comparing: {file1} vs {file2}")
    if description:
        print(f"Description: {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(['python', 'compare_json_files.py', file1, file2], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Comparison completed successfully!")
            print("Output:")
            print(result.stdout)
        else:
            print("❌ Error occurred:")
            print(result.stderr)
            
    except FileNotFoundError:
        print("❌ Error: Python not found or compare_json_files.py not found")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    print("JSON Files Comparison Tool - Example Usage")
    print("This script demonstrates how to use the comparison tool with different file pairs")
    
    # List available JSON files
    json_files = [f for f in os.listdir('.') if f.endswith('.json')]
    
    if len(json_files) < 2:
        print("❌ Need at least 2 JSON files to compare")
        return
    
    print(f"\nFound {len(json_files)} JSON files:")
    for i, file in enumerate(json_files, 1):
        print(f"  {i}. {file}")
    
    # Example comparisons
    examples = [
        ("qwen3-14B-base-mean.json", "qwen3-14B-sft-mean.json", "Base vs SFT mean comparison"),
        ("qwen3-14B-base-0.json", "qwen3-14B-sft-0.json", "Base vs SFT first run comparison"),
        ("qwen3-14B-base-1.json", "qwen3-14B-base-2.json", "Base model run 1 vs run 2"),
    ]
    
    print("\nRunning example comparisons...")
    
    for file1, file2, desc in examples:
        if os.path.exists(file1) and os.path.exists(file2):
            run_comparison(file1, file2, desc)
        else:
            print(f"\n⚠️  Skipping {file1} vs {file2} - files not found")
    
    print(f"\n{'='*60}")
    print("All comparisons completed!")
    print("Check the 'plots/' directory for generated visualizations.")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
